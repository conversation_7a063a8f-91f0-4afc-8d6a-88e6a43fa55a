<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:paddingHorizontal="10dp">

    <RelativeLayout
        android:id="@+id/user_layout"
        android:layout_width="112dp"
        android:layout_height="112dp"
        android:layout_centerVertical="true">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/user_riv"
            android:layout_width="93dp"
            android:layout_height="93dp"
            android:layout_centerInParent="true"
            android:scaleType="fitCenter"
            android:src="@mipmap/ic_launcher_app"
            app:riv_border_width="0dp"
            app:riv_corner_radius="@dimen/dp_5"
            app:riv_oval="true" />

        <ImageView
            android:id="@+id/iv_txk"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true" />

        <FrameLayout
            android:id="@+id/video_layout"
            android:layout_width="95dp"
            android:layout_height="95dp"
            android:layout_centerInParent="true">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/image_view"
                android:layout_width="95dp"
                android:layout_height="95dp"
                android:scaleType="centerCrop"
                app:riv_border_width="0dp"
                app:riv_corner_radius="@dimen/dp_5"
                tools:background="@color/gray" />

            <ImageView
                android:id="@+id/iv_player"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:layout_gravity="center"
                android:src="@mipmap/player"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </FrameLayout>

        <View
            android:id="@+id/online_view"
            android:layout_width="@dimen/dp_12"
            android:layout_height="@dimen/dp_12"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/bg_border_3fd477_40" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/dp_13"
        android:layout_toRightOf="@+id/user_layout"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/user_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="火鸡味的锅巴"
                android:textColor="@color/color_333333"
                android:textSize="@dimen/sp_16" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/user_isname_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_5"
                android:drawableLeft="@mipmap/icon_certified"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_5" />


            <!--<androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/vip_tv"
                android:visibility="gone"
                tools:visibility="visible"
                android:paddingLeft="@dimen/dp_3"
                android:layout_marginLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:drawableLeft="@mipmap/icoon_usercenter_huangguan"
                android:layout_width="wrap_content"
                android:drawableRight="@mipmap/icoon_usercenter_vip"
                android:background="@drawable/badge_262626_bg_40"
                android:textColor="@color/color_333333"
                android:layout_height="@dimen/dp_20" />-->

        </LinearLayout>

        <ImageView
            android:id="@+id/vip_tv"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_20"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_22"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/user_age_tv"
                android:layout_width="wrap_content"
                android:layout_height="20dp"

                android:gravity="center"
                android:paddingHorizontal="7dp"
                android:text="25岁"
                android:textColor="#9B9B9B"
                android:textSize="10sp" />
            <!--            android:background="@drawable/bg_border_e7_10"-->
            <View
                android:layout_width="1dp"
                android:layout_height="10dp"
                android:background="#9B9B9B" />

            <TextView
                android:id="@+id/user_height"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginStart="4dp"
                android:gravity="center"
                android:paddingHorizontal="7dp"
                android:text="168cm"
                android:textColor="#9B9B9B"
                android:textSize="10sp" />

            <View
                android:layout_width="1dp"
                android:layout_height="10dp"
                android:background="#9B9B9B" />

            <TextView
                android:id="@+id/user_career_name"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginStart="4dp"
                android:gravity="center"
                android:paddingHorizontal="7dp"
                android:text="个体经营"
                android:textColor="#9B9B9B"
                android:textSize="10sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/usericon_rv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:itemCount="1"
                tools:listitem="@layout/adapter_user_circle_pic_layout"
                />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/accost_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@mipmap/dashan"
                android:clickable="false"
                android:gravity="center" />
        </LinearLayout>


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/frient_xuan_tv"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@color/color_7E7E7E"
            android:textSize="@dimen/sp_12"
            android:visibility="gone" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp">

        <ImageView
            android:id="@+id/iv_video_call"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_call_video_left" />

        <ImageView
            android:id="@+id/chat_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:src="@mipmap/icon_chat" />
    </LinearLayout>

</RelativeLayout>
